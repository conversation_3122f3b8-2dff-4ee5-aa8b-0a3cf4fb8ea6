"""General Study Module DICOM validation - PS3.3 C.7.2.1"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig


class GeneralStudyValidator:
    """Validator for DICOM General Study Module (PS3.3 C.7.2.1)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate General Study Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Check Type 1 requirements
        if not hasattr(dataset, 'StudyInstanceUID') or not dataset.StudyInstanceUID:
            result["errors"].append("Study Instance UID (0020,000D) is required (Type 1)")
        
        # Validate sequence structures
        if config.validate_sequences:
            result = GeneralStudyValidator._validate_sequence_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate sequence structure requirements."""
        
        # Referring Physician Identification Sequence validation
        referring_phys_seq = getattr(dataset, 'ReferringPhysicianIdentificationSequence', [])
        if len(referring_phys_seq) > 1:
            result["errors"].append(
                "Referring Physician Identification Sequence (0008,0096): "
                "Only a single Item is permitted in this Sequence"
            )
        
        # Consulting Physician Identification Sequence validation
        consulting_phys_seq = getattr(dataset, 'ConsultingPhysicianIdentificationSequence', [])
        consulting_phys_name = getattr(dataset, 'ConsultingPhysiciansName', '')
        if consulting_phys_seq and consulting_phys_name:
            # Check that number and order correspond if both are present
            consulting_names = consulting_phys_name.split('\\') if isinstance(consulting_phys_name, str) else []
            if len(consulting_phys_seq) != len(consulting_names):
                result["warnings"].append(
                    "Consulting Physician Identification Sequence (0008,009D): "
                    "Number of items should correspond to Consulting Physician's Name (0008,009C)"
                )
        
        # Physician(s) of Record Identification Sequence validation
        record_phys_seq = getattr(dataset, 'PhysiciansOfRecordIdentificationSequence', [])
        record_phys_name = getattr(dataset, 'PhysiciansOfRecord', '')
        if record_phys_seq and record_phys_name:
            # Check that number and order correspond if both are present
            record_names = record_phys_name.split('\\') if isinstance(record_phys_name, str) else []
            if len(record_phys_seq) != len(record_names):
                result["warnings"].append(
                    "Physician(s) of Record Identification Sequence (0008,1049): "
                    "Number of items should correspond to Physician(s) of Record (0008,1048)"
                )
        
        # Physician(s) Reading Study Identification Sequence validation
        reading_phys_seq = getattr(dataset, 'PhysiciansReadingStudyIdentificationSequence', [])
        reading_phys_name = getattr(dataset, 'NameOfPhysiciansReadingStudy', '')
        if reading_phys_seq and reading_phys_name:
            # Check that number and order correspond if both are present
            reading_names = reading_phys_name.split('\\') if isinstance(reading_phys_name, str) else []
            if len(reading_phys_seq) != len(reading_names):
                result["warnings"].append(
                    "Physician(s) Reading Study Identification Sequence (0008,1062): "
                    "Number of items should correspond to Name of Physician(s) Reading Study (0008,1060)"
                )
        
        # Issuer of Accession Number Sequence validation
        accession_issuer_seq = getattr(dataset, 'IssuerOfAccessionNumberSequence', [])
        if len(accession_issuer_seq) > 1:
            result["errors"].append(
                "Issuer of Accession Number Sequence (0008,0051): "
                "Only a single Item is permitted in this Sequence"
            )
        
        # Requesting Service Code Sequence validation
        requesting_service_seq = getattr(dataset, 'RequestingServiceCodeSequence', [])
        if len(requesting_service_seq) > 1:
            result["errors"].append(
                "Requesting Service Code Sequence (0032,1034): "
                "Only a single Item is permitted in this Sequence"
            )
        
        return result
